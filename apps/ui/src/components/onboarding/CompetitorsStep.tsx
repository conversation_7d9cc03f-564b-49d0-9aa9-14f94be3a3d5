import React, {
  useState,
  useEffect,
  useMemo,
  use<PERSON>allback,
  useRef,
} from "react";
import { useTranslation } from "react-i18next";
import Button from "../../ui/Button";
import { Competitor as BaseCompetitor, Location } from "../../types";
import TextInput from "../../ui/form/TextInput";
import { GeocodeResult, miscService, PlacePrediction } from "../../api/misc";
import { debounce } from "lodash";
import {
  searchRetailersSupabase,
  searchNearbyRetailersSupabase,
  searchRetailersByLocationSupabase,
  RetailerResult,
} from "../../services/firestore";
import { toast } from "react-hot-toast";
import axios from "axios";
import api from "../../api";
import ManualEntryDialog from "../dialogs/ManualEntryDialog";
import {
  FacebookShareButton,
  FacebookIcon,
  TwitterShareButton,
  TwitterIcon,
  LinkedinShareButton,
  LinkedinIcon,
  PinterestShareButton,
  PinterestIcon,
  WhatsappShareButton,
  WhatsappIcon,
  RedditShareButton,
  RedditIcon,
  EmailShareButton,
  EmailIcon,
} from "react-share";
import { GoogleIcon, TrashIcon } from "../../ui/icons";
import { Modal } from "../../ui";
import MarketAnalysisInsights from "../shared/MarketAnalysisInsights";
import "../../views/competitors/Competitors.css";

// Extend the Competitor type with additional properties we need
interface Competitor extends BaseCompetitor {
  id?: number;
  isManuallyAdded?: boolean;
  website?: string;
}

// Define types for market analysis data
interface HotCategory {
  category: string;
  weight: string;
  marketAvg: number;
  yourAvg: number;
  youVsMarketPct: number;
  competitorCount: number;
  totalCompetitorProductCount: number;
  yourProductCount: number;
  averageCompetitorProductCount: number;
  competitorBreakdown: Array<{
    retailerId: string;
    retailerName: string;
    productCount: number;
  }>;
}

interface MarketSnapshot {
  competitorCount: number;
  hotCategories: HotCategory[];
  gaps: string[];
  insights: string[];
  recommendations: string[];
  categoryComparison: Array<{
    category: string;
    yourProductCount: number;
    competitorBreakdown: Array<{
      retailerId: string;
      retailerName: string;
      productCount: number;
    }>;
    totalCompetitorProducts: number;
    averageCompetitorProducts: number;
  }>;
  generatedAt: string;
  totalProductsAnalyzed: number;
  categoriesFound: number;
}

interface CompetitorsStepProps {
  selectedBusiness: Partial<Location>;
  onStepComplete: () => void;
  isCompleted: boolean;
  onBack?: () => void;
  competitors: Competitor[];
  setCompetitors: (competitor: Competitor[]) => void;
  onFinalizeOnboarding?: () => Promise<boolean>;
}

// Interface for our search results from the database
interface CompetitorSearchResult {
  id: string;
  name: string;
  address: string;
  isFromOurDatabase?: boolean;
  retailer?: RetailerResult;
  place?: PlacePrediction;
}

// Add social share function interface at the top with other interfaces
interface SocialShareFunction {
  name: string;
  icon: string;
  color: string;
  shareUrl: (url: string, text: string) => string;
}

const CompetitorsStep: React.FC<CompetitorsStepProps> = ({
  selectedBusiness,
  onStepComplete,
  isCompleted,
  onBack,
  competitors,
  setCompetitors,
  onFinalizeOnboarding,
}) => {
  const { t } = useTranslation();

  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<CompetitorSearchResult[]>(
    []
  );
  // Removed googleResults state - no longer using Google Places for competitor search
  const [showAIInsights, setShowAIInsights] = useState(false);

  // New state for market analysis data
  const [marketSnapshot, setMarketSnapshot] = useState<MarketSnapshot | null>(
    null
  );
  const [isMarketAnalysisLoading, setIsMarketAnalysisLoading] = useState(false);

  // Check if we're in quick start mode
  const location = window.location;
  const params = new URLSearchParams(location.search);
  const isQuickStart = params.get("path") === "quick";

  // Location ID for API calls
  const [locationId, setLocationId] = useState<number | null>(null);

  // Manual entry dialog state
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [manualEntrySearchQuery, setManualEntrySearchQuery] = useState("");

  // Debug function to clear localStorage - can be called from browser console
  (window as any).clearOnboardingData = () => {
    localStorage.removeItem("onboarding_business_info");
    localStorage.removeItem("onboarding_competitors");
    localStorage.removeItem("onboarding_communication");
    localStorage.removeItem("onboarding_documents");
    localStorage.removeItem("onboarding_social_platforms");
    localStorage.removeItem("onboarding_data_sources");
    localStorage.removeItem("onboarding_social_media");
    console.log("🧹 Cleared all onboarding localStorage data");
    window.location.reload();
  };

  // Debug function to show current localStorage data
  (window as any).showOnboardingData = () => {
    const businessInfo = localStorage.getItem("onboarding_business_info");
    const competitors = localStorage.getItem("onboarding_competitors");
    console.log("📦 Current localStorage data:");
    console.log(
      "Business Info:",
      businessInfo ? JSON.parse(businessInfo) : "None"
    );
    console.log("Competitors:", competitors ? JSON.parse(competitors) : "None");
    console.log("Selected Business Prop:", selectedBusiness);
    console.log("🏢 Business State for filtering:", `"${selectedBusiness.state}"`);
  };

  // Debug function to test competitor search with current business
  (window as any).testCompetitorSearch = async () => {
    const businessState = selectedBusiness.state?.trim();
    const coordinates = getBusinessCoordinates();

    console.log("🧪 Testing competitor search with:");
    console.log("  Business:", selectedBusiness.name);
    console.log("  State:", `"${businessState}"`);
    console.log("  Coordinates:", coordinates);

    if (coordinates) {
      try {
        // Use the already imported function
        const results = await searchNearbyRetailersSupabase(
          coordinates.latitude,
          coordinates.longitude,
          30,
          1,
          10,
          businessState
        );

        console.log("🔍 Test results:", results);
        console.log("📍 States found:", [...new Set(results.map((r: any) => r.state).filter(Boolean))]);

        return results;
      } catch (error) {
        console.error("❌ Test failed:", error);
      }
    } else {
      console.log("❌ No coordinates available for test - will use location-based search");
      try {
        const { searchRetailersByLocationSupabase } = await import("../../services/firestore");
        const results = await searchRetailersByLocationSupabase(
          selectedBusiness.city || "",
          selectedBusiness.state || "",
          selectedBusiness.zip || "",
          30,
          1,
          10
        );

        console.log("🔍 Location-based test results:", results);
        console.log("📍 States found:", [...new Set(results.map((r: any) => r.state).filter(Boolean))]);

        return results;
      } catch (error) {
        console.error("❌ Location-based test failed:", error);
      }
    }
  };

  // Debug function to force clear and reload competitors
  (window as any).forceReloadCompetitors = () => {
    console.log("🔄 Force clearing and reloading competitors");
    setCompetitors([]);
    localStorage.removeItem("onboarding_competitors");
    localStorage.removeItem("last_competitor_business_id");
    initialLoadCompleted.current = false;
    loadCompetitors(1);
  };

  // Debug function to completely clear all onboarding data and start fresh
  (window as any).clearAllOnboardingData = () => {
    console.log("🧹 Clearing ALL onboarding data");
    localStorage.removeItem("onboarding_business_info");
    localStorage.removeItem("onboarding_competitors");
    localStorage.removeItem("last_competitor_business_id");
    localStorage.removeItem("onboarding_communication");
    localStorage.removeItem("onboarding_documents");
    localStorage.removeItem("onboarding_social_platforms");
    localStorage.removeItem("onboarding_data_sources");
    localStorage.removeItem("onboarding_social_media");
    console.log("✅ All onboarding data cleared. Please refresh the page and start over.");
  };

  // Track the current business to detect changes
  const [currentBusinessId, setCurrentBusinessId] = useState<string | null>(
    null
  );

  // Update the state declarations for better clarity
  const [competitorPage, setCompetitorPage] = useState<number>(1);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [hasMoreCompetitors, setHasMoreCompetitors] = useState<boolean>(true);
  const initialLoadCompleted = useRef(false);
  const filteredBusinesses = useRef<Record<string, boolean>>({});

  // Fetch location ID if needed
  useEffect(() => {
    if (selectedBusiness.id) {
      setLocationId(Number(selectedBusiness.id));
    }
  }, [selectedBusiness]);

  // Detect business changes and clear competitors if business has changed
  useEffect(() => {
    // Create a more comprehensive business identifier including all location fields
    const businessId = `${
      selectedBusiness.retailer_id || selectedBusiness.name || ""
    }-${selectedBusiness.latitude || ""}-${selectedBusiness.longitude || ""}-${
      selectedBusiness.city || ""
    }-${selectedBusiness.state || ""}-${selectedBusiness.zip || ""}`;

    // If this is a different business than before, clear competitors
    if (
      currentBusinessId &&
      currentBusinessId !== businessId &&
      competitors.length > 0
    ) {
      console.log(
        `🧹 Business changed from "${currentBusinessId}" to "${businessId}", clearing competitors and related state`
      );

      // Clear competitors state
      setCompetitors([]);

      // Clear all competitor-related localStorage
      localStorage.removeItem("onboarding_competitors");
      localStorage.removeItem("last_competitor_business_id");

      // Reset competitor search state
      setSearchResults([]);
      setSearchQuery("");
      setMarketSnapshot(null);
      setShowAIInsights(false);

      // Reset pagination state
      setCompetitorPage(1);
      setHasMoreCompetitors(true);

      // Reset loading states
      setIsLoadingMore(false);
      setIsSearching(false);

      console.log("✅ Competitor state cleared for new business");
    }

    setCurrentBusinessId(businessId);
  }, [
    selectedBusiness.retailer_id,
    selectedBusiness.name,
    selectedBusiness.latitude,
    selectedBusiness.longitude,
    selectedBusiness.city,
    selectedBusiness.state,
    selectedBusiness.zip,
    currentBusinessId,
    competitors.length,
    setCompetitors,
  ]);

  // Fetch market analysis when competitors change
  useEffect(() => {
    const fetchMarketAnalysis = async () => {
      // Don't fetch if no competitors
      if (competitors.length === 0) {
        setMarketSnapshot(null);
        return;
      }

      // Don't fetch if competitors don't have meaningful data (check for at least some product data)
      const hasCompetitorProducts = competitors.some(
        (comp) => comp.isFromOurDatabase && (comp.productCount || 0) > 0
      );

      if (!hasCompetitorProducts) {
        console.log(
          "No competitors with product data found, skipping market analysis"
        );
        setMarketSnapshot(null);
        return;
      }

      console.log("Fetching market analysis for onboarding");
      setIsMarketAnalysisLoading(true);

      try {
        const data = await api.marketAnalysis.getAnalysis(
          competitors,
          selectedBusiness.retailer_id
        );

        console.log(`Received market snapshot:`, data);
        setMarketSnapshot(data);
      } catch (error) {
        console.error("Error fetching market analysis:", error);
        toast.error("Unable to fetch market analysis");
        setMarketSnapshot(null);
      } finally {
        setIsMarketAnalysisLoading(false);
      }
    };

    // Debounce the call to avoid excessive API calls while adding/removing competitors
    const debouncedFetch = debounce(fetchMarketAnalysis, 500);
    debouncedFetch();

    // Cleanup the debounced function on unmount
    return () => {
      debouncedFetch.cancel();
    };
  }, [competitors, selectedBusiness.retailer_id]);

  // Add a function to check if a competitor is the same as the selected business
  const isOwnBusiness = useCallback(
    (competitor: RetailerResult | Competitor): boolean => {
      // Skip if it's the same business by ID
      if (
        selectedBusiness.retailer_id &&
        ((competitor as RetailerResult).id === selectedBusiness.retailer_id ||
          (competitor as Competitor).place_id === selectedBusiness.retailer_id)
      ) {
        return true;
      }

      // Skip if same name
      const competitorName =
        (competitor as RetailerResult).dispensary_name ||
        (competitor as Competitor).name ||
        "";
      if (
        competitorName.toLowerCase() === selectedBusiness.name?.toLowerCase()
      ) {
        return true;
      }

      // Skip if coordinates are very close (within 0.0001 degree, approx 10 meters)
      const competitorLat =
        (competitor as RetailerResult).latitude ||
        (competitor as Competitor).location?.lat;
      const competitorLng =
        (competitor as RetailerResult).longitude ||
        (competitor as Competitor).location?.lng;

      if (
        selectedBusiness.latitude &&
        selectedBusiness.longitude &&
        competitorLat &&
        competitorLng &&
        Math.abs(competitorLat - selectedBusiness.latitude) < 0.0001 &&
        Math.abs(competitorLng - selectedBusiness.longitude) < 0.0001
      ) {
        return true;
      }

      return false;
    },
    [selectedBusiness]
  );

  const debouncedSearchPlaces = useMemo(
    () =>
      debounce(async (query: string) => {
        if (!query) return;

        try {
          setIsSearching(true);

          // First, search in our database using Supabase
          const retailers = await searchRetailersSupabase(query, true);

          // Filter out the selected business from the results using the isOwnBusiness function
          const filteredRetailers = retailers.filter(
            (retailer: RetailerResult) => !isOwnBusiness(retailer)
          );

          // Log filtered results
          if (retailers.length !== filteredRetailers.length) {
            console.log(
              `Filtered out ${
                retailers.length - filteredRetailers.length
              } results that match your selected business "${
                selectedBusiness.name
              }"`
            );
          }

          // Transform retailer results to common format
          const retailerResults: CompetitorSearchResult[] =
            filteredRetailers.map((retailer: RetailerResult) => ({
              id: retailer.id,
              name: retailer.dispensary_name,
              address: retailer.physical_address
                ? `${retailer.physical_address}, ${retailer.city}, ${retailer.state} ${retailer.zip_code}`
                : `${retailer.city}, ${retailer.state} ${retailer.zip_code}`,
              isFromOurDatabase: true,
              retailer,
            }));

          // Always use database results only - no server-side fallback for competitors
          setSearchResults(retailerResults);
          setIsSearching(false);

          // Log search results for debugging
          console.log(
            `Database search for "${query}" found ${retailerResults.length} competitors`
          );
        } catch (error) {
          console.error("Error searching places:", error);
          // On error, return empty results
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      }, 300),
    [selectedBusiness, isOwnBusiness]
  );

  const searchCompetitors = useCallback(() => {
    if (!searchQuery) return;
    debouncedSearchPlaces(searchQuery);
  }, [searchQuery, debouncedSearchPlaces]);

  useEffect(() => {
    if (!searchQuery) {
      setSearchResults([]);
      return;
    }
    searchCompetitors();
  }, [searchQuery, searchCompetitors]);

  // Show AI insights after auto-populate in quick start mode
  useEffect(() => {
    if (isQuickStart && competitors.length > 0) {
      setShowAIInsights(true);
    }
  }, [isQuickStart, competitors.length]);

  // Filter out the selected business from the competitors list on component mount
  useEffect(() => {
    if (selectedBusiness && competitors.length > 0) {
      const filteredCompetitors = competitors.filter(
        (comp) => !isOwnBusiness(comp)
      );

      if (filteredCompetitors.length !== competitors.length) {
        console.log(
          `Removed ${
            competitors.length - filteredCompetitors.length
          } competitors that match selected business`
        );
        setCompetitors(filteredCompetitors);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedBusiness]);

  // Function to save competitor to backend
  const saveCompetitorToBackend = async (competitor: Competitor) => {
    if (!locationId) return;

    try {
      console.log(
        `Saving competitor ${competitor.name} to backend for location ${locationId}`
      );
      const response = await axios.post(
        `/locations/${locationId}/competitors`,
        {
          competitor_place_id: competitor.place_id,
          name: competitor.name,
          address: competitor.address,
          latitude: competitor.location?.lat || 0,
          longitude: competitor.location?.lng || 0,
          distance_km: competitor.distance || 0, // Use || 0 to handle undefined
        }
      );

      console.log("Competitor saved successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error saving competitor to backend:", error);
      // Don't throw error - we can still continue with the competitor in the UI
    }
  };

  // Modify handleResultSelect to only handle database results
  const handleResultSelect = async (result: CompetitorSearchResult) => {
    // First check if this is the user's own business
    if (result.retailer && isOwnBusiness(result.retailer)) {
      toast.error("You cannot add your own business as a competitor");
      return;
    }

    if (result.isFromOurDatabase && result.retailer) {
      // Convert retailer to competitor
      const retailer = result.retailer;

      // Check if competitor already exists
      if (competitors.some((comp) => comp.place_id === retailer.id)) {
        toast.success("Competitor already added to your list");
        return;
      }

      const newCompetitor: Competitor = {
        place_id: retailer.id,
        name: retailer.dispensary_name,
        address: retailer.physical_address
          ? `${retailer.physical_address}, ${retailer.city}, ${retailer.state} ${retailer.zip_code}`
          : `${retailer.city}, ${retailer.state} ${retailer.zip_code}`,
        location: {
          lat: retailer.latitude,
          lng: retailer.longitude,
        },
        distance: 0, // Will be calculated if needed
        isFromOurDatabase: true,
        productCount: retailer.productCount || 0, // Include product count
      };

      // Save to backend
      await saveCompetitorToBackend(newCompetitor);

      // Add the competitor to local state
      setCompetitors([...competitors, newCompetitor]);
      toast.success(
        `Added ${retailer.dispensary_name} to your competitors list`
      );
    } else {
      // This should not happen anymore since we only search the database
      console.warn(
        "Received non-database result in handleResultSelect:",
        result
      );
      toast.error("Invalid competitor selection");
    }

    // Clear search state
    setSearchQuery("");
    setSearchResults([]);
  };

  // handlePlaceSelect function removed - no longer using Google Places for competitor search

  // Update handleAddCustomCompetitor to show the manual entry dialog
  const handleAddCustomCompetitor = async (name: string) => {
    setManualEntrySearchQuery(name);
    setShowManualEntry(true);

    // Clear search state
    setSearchQuery("");
    setSearchResults([]);
  };

  // New function to handle saving from manual entry dialog
  const handleSaveManualCompetitor = (competitorData: Partial<Location>) => {
    // Only proceed if we have a name
    if (!competitorData.name) return;

    const customCompetitor: Competitor = {
      place_id: `custom-${Date.now()}`,
      name: competitorData.name,
      address: competitorData.address
        ? `${competitorData.address}${
            competitorData.city ? `, ${competitorData.city}` : ""
          }${competitorData.state ? `, ${competitorData.state}` : ""}${
            competitorData.zip ? ` ${competitorData.zip}` : ""
          }`
        : t("onboarding.steps.competitors.custom_address", "Custom address"),
      location: {
        lat: competitorData.latitude || 0,
        lng: competitorData.longitude || 0,
      },
      distance: 0,
      isFromOurDatabase: false,
      isManuallyAdded: true,
      website: competitorData.website,
    };

    // Save to backend
    saveCompetitorToBackend(customCompetitor);

    // Add the competitor to local state
    setCompetitors([...competitors, customCompetitor]);

    // Close the dialog
    setShowManualEntry(false);

    // Show success message
    toast.success(`Added ${customCompetitor.name} to your competitors list`);
  };

  // Update removeCompetitor to delete from backend
  const removeCompetitor = async (index: number) => {
    const competitor = competitors[index];
    const updatedCompetitors = [...competitors];
    updatedCompetitors.splice(index, 1);
    setCompetitors(updatedCompetitors);

    // If we have a location ID, try to delete from backend
    if (locationId && competitor.id) {
      try {
        await axios.delete(
          `/locations/${locationId}/competitors/${competitor.id}`
        );
        console.log(`Competitor ${competitor.name} deleted from backend`);
      } catch (error) {
        console.error("Error deleting competitor from backend:", error);
        // Don't revert the UI state - just log the error
      }
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  const handleContinue = async () => {
    // Save to localStorage
    try {
      localStorage.setItem(
        "onboarding_competitors",
        JSON.stringify(competitors)
      );
    } catch (error) {
      console.error("Failed to save competitors to localStorage:", error);
    }

    // If in quick start mode, we need to finalize the onboarding here
    if (isQuickStart && onFinalizeOnboarding) {
      try {
        const success = await onFinalizeOnboarding();
        if (success) {
          // The onFinalizeOnboarding function will handle showing the schedule call modal
          // so we don't need to call onStepComplete which would navigate to chat
          return;
        } else {
          // If finalizing failed, just complete normally
          onStepComplete();
        }
      } catch (error) {
        console.error("Error finalizing onboarding:", error);
        // Just complete normally if there was an error
        onStepComplete();
      }
    } else {
      // For regular flow, just move to next step
      onStepComplete();
    }
  };

  const handleSkip = () => {
    // If in quick start mode, we need to finalize the onboarding here
    if (isQuickStart && onFinalizeOnboarding) {
      onFinalizeOnboarding()
        .then((success) => {
          // The onFinalizeOnboarding function will handle showing the schedule call modal
          // so we don't need to call onStepComplete
        })
        .catch((error) => {
          console.error("Error finalizing onboarding:", error);
          // Just complete normally if there was an error
          onStepComplete();
        });
    } else {
      // For regular flow, just move to next step
      onStepComplete();
    }
  };

  // Update the business tracking in the initial load useEffect

  // Remove the competitors from the useEffect dependency to prevent circular renders
  useEffect(() => {
    if (isQuickStart && competitors.length > 0) {
      setShowAIInsights(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isQuickStart]); // Remove competitors from dependency array

  // Update the filter useEffect to use the filteredBusinesses ref
  useEffect(() => {
    // Track whether we've filtered for this specific business
    const businessKey =
      selectedBusiness?.name || selectedBusiness?.retailer_id || "";

    // Only run this filter once per business selection
    if (
      selectedBusiness &&
      competitors.length > 0 &&
      !filteredBusinesses.current[businessKey]
    ) {
      filteredBusinesses.current[businessKey] = true;

      const filteredCompetitors = competitors.filter(
        (comp) => !isOwnBusiness(comp)
      );

      if (filteredCompetitors.length !== competitors.length) {
        console.log(
          `Removed ${
            competitors.length - filteredCompetitors.length
          } competitors that match selected business`
        );
        setCompetitors(filteredCompetitors);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedBusiness, competitors]);

  // Helper function to get business coordinates from multiple sources
  const getBusinessCoordinates = useCallback(() => {
    console.log(`🔍 GETTING BUSINESS COORDINATES:`, {
      selectedBusinessLat: selectedBusiness.latitude,
      selectedBusinessLng: selectedBusiness.longitude,
      selectedBusinessName: selectedBusiness.name,
      selectedBusinessAddress: selectedBusiness.address,
      selectedBusinessCity: selectedBusiness.city,
      selectedBusinessState: selectedBusiness.state,
      selectedBusinessZip: selectedBusiness.zip,
    });

    // Get coordinates from localStorage to compare
    let localStorageCoords = null;
    try {
      const savedBusinessInfo = localStorage.getItem(
        "onboarding_business_info"
      );
      if (savedBusinessInfo) {
        const businessInfo = JSON.parse(savedBusinessInfo);
        console.log(`📦 localStorage business info:`, businessInfo);
        if (businessInfo.latitude && businessInfo.longitude) {
          localStorageCoords = {
            latitude: businessInfo.latitude,
            longitude: businessInfo.longitude,
            address: businessInfo.address,
            city: businessInfo.city,
            state: businessInfo.state,
            zip: businessInfo.zip,
          };
        }
      }
    } catch (error) {
      console.error("Error reading business info from localStorage:", error);
    }

    // Check if selectedBusiness has coordinates
    if (selectedBusiness.latitude && selectedBusiness.longitude) {
      console.log(
        `✅ Found coordinates in selectedBusiness prop: lat=${selectedBusiness.latitude}, lng=${selectedBusiness.longitude}`
      );

      // Validate that these coordinates match the expected business state
      if (selectedBusiness.state) {
        const businessState = selectedBusiness.state.trim().toUpperCase();
        const lat = selectedBusiness.latitude;
        const lng = selectedBusiness.longitude;

        // Define coordinate bounds for major states
        const stateBounds: Record<string, {minLat: number, maxLat: number, minLng: number, maxLng: number}> = {
          'CA': { minLat: 32.5, maxLat: 42.0, minLng: -124.5, maxLng: -114.1 }, // California
          'CALIFORNIA': { minLat: 32.5, maxLat: 42.0, minLng: -124.5, maxLng: -114.1 },
          'IL': { minLat: 36.9, maxLat: 42.5, minLng: -91.5, maxLng: -87.0 }, // Illinois
          'ILLINOIS': { minLat: 36.9, maxLat: 42.5, minLng: -91.5, maxLng: -87.0 },
          'MI': { minLat: 41.7, maxLat: 48.3, minLng: -90.5, maxLng: -82.1 }, // Michigan
          'MICHIGAN': { minLat: 41.7, maxLat: 48.3, minLng: -90.5, maxLng: -82.1 },
          'NY': { minLat: 40.4, maxLat: 45.0, minLng: -79.8, maxLng: -71.8 }, // New York
          'NEW YORK': { minLat: 40.4, maxLat: 45.0, minLng: -79.8, maxLng: -71.8 },
          'TX': { minLat: 25.8, maxLat: 36.5, minLng: -106.6, maxLng: -93.5 }, // Texas
          'TEXAS': { minLat: 25.8, maxLat: 36.5, minLng: -106.6, maxLng: -93.5 },
          'FL': { minLat: 24.4, maxLat: 31.0, minLng: -87.6, maxLng: -79.8 }, // Florida
          'FLORIDA': { minLat: 24.4, maxLat: 31.0, minLng: -87.6, maxLng: -79.8 },
        };

        const bounds = stateBounds[businessState];
        if (bounds) {
          const withinBounds = lat >= bounds.minLat && lat <= bounds.maxLat &&
                             lng >= bounds.minLng && lng <= bounds.maxLng;

          if (!withinBounds) {
            console.warn(
              `🚨 COORDINATE MISMATCH: Business coordinates (${lat}, ${lng}) are outside expected bounds for ${businessState}`
            );
            console.warn(
              `Expected ${businessState} bounds: lat ${bounds.minLat}-${bounds.maxLat}, lng ${bounds.minLng}-${bounds.maxLng}`
            );
            console.warn(
              `❌ These coordinates will find competitors in the wrong state! Rejecting coordinates.`
            );

            // Check if localStorage has better coordinates
            if (
              localStorageCoords &&
              localStorageCoords.latitude >= bounds.minLat &&
              localStorageCoords.latitude <= bounds.maxLat &&
              localStorageCoords.longitude >= bounds.minLng &&
              localStorageCoords.longitude <= bounds.maxLng
            ) {
              console.log(
                `✅ Using localStorage coordinates instead as they're within ${businessState} bounds`
              );
              return {
                latitude: localStorageCoords.latitude,
                longitude: localStorageCoords.longitude,
                source: "localStorage-corrected",
              };
            }

            // Reject invalid coordinates - this will force fallback to location-based search
            console.log(`❌ Rejecting invalid coordinates, will use location-based search instead`);
            return null;
          } else {
            console.log(
              `✅ Coordinates are within expected bounds for ${businessState}`
            );
          }
        } else {
          console.log(
            `⚠️ No coordinate bounds defined for state "${businessState}", using coordinates as-is`
          );
        }
      }

      return {
        latitude: selectedBusiness.latitude,
        longitude: selectedBusiness.longitude,
        source: "selectedBusiness",
      };
    }

    // Fallback to localStorage if coordinates are missing from prop
    if (localStorageCoords) {
      console.log(
        `✅ Using coordinates from localStorage as fallback: lat=${localStorageCoords.latitude}, lng=${localStorageCoords.longitude}`
      );
      return {
        latitude: localStorageCoords.latitude,
        longitude: localStorageCoords.longitude,
        source: "localStorage",
      };
    }

    console.log(`❌ No valid coordinates found from any source`);
    return null;
  }, [
    selectedBusiness.latitude,
    selectedBusiness.longitude,
    selectedBusiness.state,
    selectedBusiness.address,
    selectedBusiness.city,
    selectedBusiness.zip,
    selectedBusiness.name,
  ]);

  // Update the loadCompetitors function
  const loadCompetitors = useCallback(
    async (page: number) => {
      // Debug: Log the selectedBusiness data to see what we're working with
      console.log(
        `🔍 CompetitorsStep loadCompetitors - selectedBusiness data:`,
        {
          name: selectedBusiness.name,
          address: selectedBusiness.address,
          city: selectedBusiness.city,
          state: selectedBusiness.state,
          zip: selectedBusiness.zip,
          latitude: selectedBusiness.latitude,
          longitude: selectedBusiness.longitude,
          searchMethod: selectedBusiness.searchMethod,
        }
      );

      // Priority 1: ALWAYS try coordinate-based search first (lat/lng)
      const coordinates = getBusinessCoordinates();

      // Avoid duplicate calls - if already loading, don't start a new load
      if (isLoadingMore) {
        console.log("Already loading competitors, skipping duplicate request");
        return;
      }

      setIsLoadingMore(true);
      let resultsFound = false;

      if (coordinates) {
        const businessState = selectedBusiness.state?.trim();
        console.log(
          `🎯 Using coordinate-based search: lat=${coordinates.latitude}, lng=${coordinates.longitude}, preferredState="${businessState}"`
        );

        if (!businessState) {
          console.warn("⚠️ No business state provided for filtering - competitors from all states will be shown");
        }

        try {
          const nearbyRetailers = await searchNearbyRetailersSupabase(
            coordinates.latitude,
            coordinates.longitude,
            30, // Fixed 30-mile radius
            page,
            4, // Number of results per page
            businessState // Pass the business state as preferred state
          );

          console.log(
            `🔍 Received ${nearbyRetailers.length} retailers from API for state "${businessState}"`
          );

          // Log the states of returned retailers for debugging
          if (nearbyRetailers.length > 0) {
            const retailerStates = nearbyRetailers.map(r => r.state).filter(Boolean);
            const uniqueStates = [...new Set(retailerStates)];
            console.log(`📍 Returned retailers from states: ${uniqueStates.join(', ')}`);

            // Log each retailer for detailed debugging
            nearbyRetailers.forEach((retailer, index) => {
              console.log(`  ${index + 1}. ${retailer.dispensary_name} - ${retailer.city}, ${retailer.state} ${retailer.zip_code || ''}`);
            });
          }

          if (nearbyRetailers.length > 0) {
            resultsFound = true;
            // Filter out the selected business from the nearby retailers
            const filteredRetailers = nearbyRetailers.filter(
              (retailer: RetailerResult) => !isOwnBusiness(retailer)
            );

            // Log filtered results
            if (nearbyRetailers.length !== filteredRetailers.length) {
              console.log(
                `Auto-populate: Filtered out ${
                  nearbyRetailers.length - filteredRetailers.length
                } results that match your selected business "${
                  selectedBusiness.name
                }"`
              );
            }

            // Convert retailers to competitors
            const dbCompetitors: Competitor[] = filteredRetailers.map(
              (retailer: RetailerResult) => ({
                place_id: retailer.id,
                name: retailer.dispensary_name,
                address: retailer.physical_address
                  ? `${retailer.physical_address}, ${retailer.city}, ${
                      retailer.state
                    } ${retailer.zip_code || ""}`
                  : `${retailer.city}, ${retailer.state} ${
                      retailer.zip_code || ""
                    }`,
                location: {
                  lat: retailer.latitude,
                  lng: retailer.longitude,
                },
                distance: retailer.distance || 0,
                isFromOurDatabase: true,
                productCount: retailer.productCount || 0, // Include product count
              })
            );

            // Append new competitors instead of replacing them
            // Filter out any that already exist based on place_id
            const existingIds = new Set(competitors.map((c) => c.place_id));
            const newCompetitors = dbCompetitors.filter(
              (c) => !existingIds.has(c.place_id)
            );

            if (newCompetitors.length > 0) {
              setCompetitors([...competitors, ...newCompetitors]);

              // Set next page number (but don't update state for the current operation)
              const nextPage = page + 1;
              setCompetitorPage(nextPage);

              // Show AI insights after a short delay (if in quick start mode)
              if (isQuickStart) {
                setTimeout(() => {
                  setShowAIInsights(true);
                }, 800);
              }

              toast.success(`Found ${newCompetitors.length} new competitors!`);
              setHasMoreCompetitors(true);
            } else {
              // No new competitors found - we've reached the end for this method
              setHasMoreCompetitors(false);
            }
          }
        } catch (error) {
          console.error(
            "Error finding nearby competitors by coordinates:",
            error
          );
        }
      }

      // Fallback to location-based search if coordinate search fails or finds nothing
      if (!resultsFound) {
        if (!coordinates) {
          console.log(
            `🔄 No valid coordinates available (likely rejected due to state mismatch), using location-based search with city="${selectedBusiness.city}", state="${selectedBusiness.state}", zip="${selectedBusiness.zip}"`
          );
        } else {
          console.log(
            `⚠️ No results from coordinate search, falling back to location-based search with city="${selectedBusiness.city}", state="${selectedBusiness.state}", zip="${selectedBusiness.zip}"`
          );
        }
        try {
          const locationRetailers = await searchRetailersByLocationSupabase(
            selectedBusiness.city || "",
            selectedBusiness.state || "",
            selectedBusiness.zip || "",
            30,
            page,
            4
          );

          if (locationRetailers.length > 0) {
            resultsFound = true;
            const filteredRetailers = locationRetailers.filter(
              (retailer: RetailerResult) => !isOwnBusiness(retailer)
            );

            const dbCompetitors: Competitor[] = filteredRetailers.map(
              (retailer: RetailerResult) => ({
                place_id: retailer.id,
                name: retailer.dispensary_name,
                address: retailer.physical_address
                  ? `${retailer.physical_address}, ${retailer.city}, ${
                      retailer.state
                    } ${retailer.zip_code || ""}`
                  : `${retailer.city}, ${retailer.state} ${
                      retailer.zip_code || ""
                    }`,
                location: {
                  lat: retailer.latitude,
                  lng: retailer.longitude,
                },
                distance: retailer.distance || 0,
                isFromOurDatabase: true,
                productCount: retailer.productCount || 0,
              })
            );

            const existingIds = new Set(competitors.map((c) => c.place_id));
            const newCompetitors = dbCompetitors.filter(
              (c) => !existingIds.has(c.place_id)
            );

            if (newCompetitors.length > 0) {
              setCompetitors([...competitors, ...newCompetitors]);
              setCompetitorPage(page + 1);
              toast.success(
                `Found ${newCompetitors.length} new competitors by location!`
              );
              setHasMoreCompetitors(true);
            } else {
              setHasMoreCompetitors(false);
            }
          }
        } catch (error) {
          console.error("Error finding nearby competitors by location:", error);
        }
      }

      if (!resultsFound) {
        console.log(
          `No competitors found for business in ${selectedBusiness.state || "area"}`
        );
        setHasMoreCompetitors(false);
      }
      setIsLoadingMore(false);
    },
    [
      selectedBusiness.city,
      selectedBusiness.state,
      selectedBusiness.zip,
      selectedBusiness.name,
      selectedBusiness.address,
      selectedBusiness.latitude,
      selectedBusiness.longitude,
      selectedBusiness.searchMethod,
      getBusinessCoordinates,
      competitors,
      isLoadingMore,
      setCompetitors,
      setCompetitorPage,
      isQuickStart,
      setShowAIInsights,
      isOwnBusiness,
    ]
  );

  // useEffect to handle business changes and initial competitor loading
  useEffect(() => {
    // Reset pagination when business changes
    setCompetitorPage(1);
    setHasMoreCompetitors(true);

    // Reset tracking if business changes completely
    const businessId = selectedBusiness?.id || selectedBusiness?.retailer_id;
    const businessName = selectedBusiness?.name;

    // Create a unique identifier for the current business including all location fields
    const currentBusinessIdentifier = `${businessId || ""}-${
      businessName || ""
    }-${selectedBusiness?.latitude || ""}-${selectedBusiness?.longitude || ""}-${
      selectedBusiness?.city || ""
    }-${selectedBusiness?.state || ""}-${selectedBusiness?.zip || ""}`;

    // Check if this is a different business than what we last loaded competitors for
    const lastBusinessIdentifier = localStorage.getItem(
      "last_competitor_business_id"
    );
    const isNewBusiness = currentBusinessIdentifier !== lastBusinessIdentifier;

    if (isNewBusiness && (businessId || businessName)) {
      console.log(
        `🔄 New business detected: "${currentBusinessIdentifier}", resetting competitor tracking and loading state`
      );

      // Reset all tracking for the new business
      filteredBusinesses.current = {};
      initialLoadCompleted.current = false; // Reset initial load flag for new business

      // Clear any existing competitors from previous business
      if (competitors.length > 0) {
        console.log("Clearing competitors from previous business");
        setCompetitors([]);
        localStorage.removeItem("onboarding_competitors");
      }

      // Store the new business identifier
      localStorage.setItem(
        "last_competitor_business_id",
        currentBusinessIdentifier
      );
    }

    // Auto-load competitors for new business
    const coordinates = getBusinessCoordinates();
    const shouldPerformInitialLoad = !initialLoadCompleted.current;

    if (shouldPerformInitialLoad) {
      if (coordinates) {
        console.log(
          `Performing initial competitor load for business "${businessName}" using coordinates from ${coordinates.source}`
        );
        initialLoadCompleted.current = true; // Set this immediately to prevent multiple loads for this business
        loadCompetitors(1); // Always use page 1 for initial load
      } else {
        // No valid coordinates (likely rejected due to state mismatch)
        // Clear any existing competitors and force location-based search
        console.log(
          `No valid coordinates for business "${businessName}" (likely rejected due to state mismatch), clearing existing competitors and forcing location-based search`
        );

        if (competitors.length > 0) {
          console.log("Clearing existing competitors due to invalid coordinates");
          setCompetitors([]);
          localStorage.removeItem("onboarding_competitors");
        }

        // Force location-based search by calling loadCompetitors
        initialLoadCompleted.current = true;
        loadCompetitors(1);
      }
    }
  }, [
    selectedBusiness.latitude,
    selectedBusiness.longitude,
    selectedBusiness.id,
    selectedBusiness.name,
    selectedBusiness.retailer_id,
    selectedBusiness.city,
    selectedBusiness.state,
    selectedBusiness.zip,
    getBusinessCoordinates,
    loadCompetitors,
    competitors.length,
    setCompetitors,
  ]);

  // Update handleAutoPopulate to use the new loadCompetitors function
  const handleAutoPopulate = () => {
    // When clicking "Load More", use the current page number which was set after the previous load
    loadCompetitors(competitorPage);
  };

  // Enhance the search results display to better highlight database entries
  const renderSearchResults = () => {
    if (!searchQuery.trim() || isSearching) {
      return null;
    }

    return (
      <div className="mt-2 rounded-lg shadow-sm border border-divider">
        <ul className="divide-y divide-divider max-h-[300px] overflow-y-auto">
          {/* Sort results to prioritize database entries with product data */}
          {searchResults
            .sort((a, b) => {
              // First prioritize database entries with product count
              if (a.isFromOurDatabase && b.isFromOurDatabase) {
                const aCount = a.retailer?.productCount || 0;
                const bCount = b.retailer?.productCount || 0;
                if (aCount > 0 && bCount === 0) return -1;
                if (aCount === 0 && bCount > 0) return 1;
                if (aCount !== bCount) return bCount - aCount; // Higher product count first
              }

              // Show database search results
              if (a.isFromOurDatabase && !b.isFromOurDatabase) return -1;
              if (!a.isFromOurDatabase && b.isFromOurDatabase) return 1;

              // Default to alphabetical sort
              return a.name.localeCompare(b.name);
            })
            .map((result) => (
              <li
                key={result.id}
                className={`p-3 hover:bg-surface-secondary cursor-pointer ${
                  result.isFromOurDatabase ? "border-l-2 border-[#3EDC81]" : ""
                }`}
                onClick={() => handleResultSelect(result)}
              >
                <div className="text-sm font-medium flex items-center">
                  {result.name}
                  {result.isFromOurDatabase ? (
                    <div className="flex items-center">
                      <span className="ml-2 text-[#3EDC81]">★</span>
                      {result.retailer &&
                        (result.retailer.productCount || 0) > 0 && (
                          <span className="ml-2 text-xs text-[#3EDC81] rounded-full bg-[#3EDC81]/10 px-2 py-0.5">
                            {result.retailer.productCount} products
                          </span>
                        )}
                      {result.retailer &&
                        (!result.retailer.productCount ||
                          result.retailer.productCount === 0) && (
                          <span className="ml-2 text-xs text-orange-500 rounded-full bg-orange-100 px-2 py-0.5">
                            No products
                          </span>
                        )}
                    </div>
                  ) : (
                    <div
                      className="flex items-center ml-2"
                      style={{ width: "14px", height: "4px" }}
                    >
                      <GoogleIcon />
                    </div>
                  )}
                </div>
                <div className="text-sm text-primary-soft">
                  {result.address}
                </div>
              </li>
            ))}

          {/* No results message */}
          {searchQuery && searchResults.length === 0 && (
            <li className="p-3 text-sm text-center text-primary-soft">
              {t("onboarding.steps.competitors.no_results", "No results found")}
            </li>
          )}
        </ul>
      </div>
    );
  };

  // Render the AI insights with real data using the MarketAnalysisInsights component
  const renderAIInsights = () => {
    return (
      <MarketAnalysisInsights
        competitors={competitors}
        businessName={selectedBusiness.name || "your dispensary"}
        marketSnapshot={marketSnapshot}
        isLoading={isMarketAnalysisLoading}
        error={null}
        isOnboarding={true}
        locationId={locationId || undefined}
      />
    );
  };

  return (
    <div>
      {/* Business Name Display Section - Redesigned to be more compact */}
      <div className="flex items-center justify-between bg-surface-secondary border border-divider rounded-lg p-3 mb-4">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-primary-accent/20 flex items-center justify-center mr-2">
            <span>🏪</span>
          </div>
          <div>
            <h3 className="text-base font-medium">{selectedBusiness.name}</h3>
            <p className="text-xs text-primary-soft">
              {selectedBusiness.address ||
                `${selectedBusiness.city || ""} ${
                  selectedBusiness.state || ""
                } ${selectedBusiness.zip || ""}`}
            </p>
          </div>
        </div>
        <div className="text-sm ml-4 max-w-[50%]">
          <span className="text-primary-soft">
            {t(
              "onboarding.steps.competitors.add_competitors_for",
              "Adding competitors for your business"
            )}
          </span>
        </div>
      </div>

      {/* AI Competitive Insights - Only shown in Quick Start mode after competitors loaded */}
      {showAIInsights && isQuickStart && renderAIInsights()}

      {/* Manual Entry Dialog */}
      <ManualEntryDialog
        open={showManualEntry}
        onClose={() => setShowManualEntry(false)}
        onSave={handleSaveManualCompetitor}
        searchQuery={manualEntrySearchQuery}
        isCompetitor={true}
      />

      {/* Two-column layout for Manual Add and Competitor List */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
        {/* Manual search section - Always visible now, even in Quick Start mode */}
        <div className="bg-surface border border-divider rounded-lg p-3 shadow-sm">
          <h3 className="text-base font-medium mb-2 text-primary">
            {isQuickStart ? "Add More Competitors" : "Manually Add Competitors"}
          </h3>

          <div className="mb-3">
            <TextInput
              name=""
              value={searchQuery}
              onChange={(value) => setSearchQuery(value)}
              placeholder={t("onboarding.steps.competitors.search_placeholder")}
            />
          </div>

          {/* Search Results */}
          {renderSearchResults()}

          {!searchQuery && (
            <div className="mt-2">
              <p className="text-xs text-primary-soft text-center">
                {t(
                  "onboarding.steps.competitors.search_tip",
                  "Search for competitors by name or address"
                )}
              </p>
            </div>
          )}

          {/* Always show Manual Entry button */}
          {searchQuery.trim() && (
            <div className="mt-4 flex justify-center">
              <Button
                variant="secondary"
                onClick={() => handleAddCustomCompetitor(searchQuery)}
                className="w-full max-w-md"
              >
                <span className="mr-2">✏️</span>
                {t(
                  "onboarding.steps.competitors.enter_manually",
                  "Enter manually"
                )}
              </Button>
            </div>
          )}
        </div>

        <div className="bg-surface border border-divider rounded-lg p-3 shadow-sm">
          <h3 className="text-base font-medium mb-2 text-primary">
            {t("onboarding.steps.competitors.added_list", "Added Competitors")}
          </h3>

          {competitors.length > 0 ? (
            <div>
              <ul className="divide-y divide-divider max-h-[250px] overflow-y-auto mb-3">
                {competitors
                  .filter((competitor) => !isOwnBusiness(competitor))
                  .map((competitor, index) => (
                    <li key={competitor.place_id} className="py-2">
                      <div className="flex items-center justify-between w-full">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm flex items-center">
                            {competitor.name}
                            {competitor.isFromOurDatabase && (
                              <span className="ml-1 text-[#3EDC81]">★</span>
                            )}
                            {competitor.isManuallyAdded && (
                              <span className="ml-1 text-xs text-blue-500 rounded-full bg-blue-100 px-1.5 py-0.5">
                                Manual
                              </span>
                            )}
                            {!competitor.isFromOurDatabase &&
                              !competitor.isManuallyAdded && (
                                <div
                                  className="flex items-center ml-2"
                                  style={{ width: "14px", height: "4px" }}
                                >
                                  <GoogleIcon />
                                </div>
                              )}
                          </div>
                          <div className="text-xs text-primary-soft truncate">
                            {competitor.address}
                          </div>
                          {competitor.distance > 0 && (
                            <div className="text-xs text-primary-soft">
                              {competitor.distance.toFixed(1)} miles away
                            </div>
                          )}
                          {competitor.isManuallyAdded && competitor.website && (
                            <div className="text-xs text-primary-soft">
                              <a
                                href={
                                  competitor.website.startsWith("http")
                                    ? competitor.website
                                    : `https://${competitor.website}`
                                }
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-500 hover:underline"
                              >
                                {competitor.website}
                              </a>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 ml-4 min-w-[120px] justify-end">
                          {competitor.isFromOurDatabase &&
                            (competitor.productCount || 0) > 0 && (
                              <span className="text-xs text-[#3EDC81] rounded-full bg-[#3EDC81]/10 px-2 py-0.5 min-w-[90px] text-center">
                                {competitor.productCount} products
                              </span>
                            )}
                          {competitor.isFromOurDatabase &&
                            (!competitor.productCount ||
                              competitor.productCount === 0) && (
                              <span className="text-xs text-orange-500 rounded-full bg-orange-100 px-2 py-0.5 min-w-[90px] text-center">
                                No products
                              </span>
                            )}
                          <Button
                            variant="secondary"
                            onClick={() => removeCompetitor(index)}
                            className="text-xs px-2 py-1"
                            icon={<TrashIcon />}
                          />
                        </div>
                      </div>
                    </li>
                  ))}
              </ul>

              {/* Find more/nearby competitors button */}
              <Button
                onClick={handleAutoPopulate}
                variant="secondary"
                disabled={
                  isLoadingMore ||
                  !getBusinessCoordinates() ||
                  !hasMoreCompetitors
                }
                className="w-full mt-2"
              >
                {isLoadingMore ? (
                  <span className="inline-block animate-spin mr-2">⏳</span>
                ) : (
                  <span className="mr-2">🔍</span>
                )}
                {!hasMoreCompetitors
                  ? t(
                      "onboarding.steps.competitors.no_more_competitors",
                      "No More Competitors Found"
                    )
                  : competitors.length > 0
                  ? t(
                      "onboarding.steps.competitors.load_more",
                      "Load More Competitors"
                    )
                  : t(
                      "onboarding.steps.competitors.find_competitors",
                      "Find Competitors"
                    )}
              </Button>
            </div>
          ) : (
            <div className="text-center text-primary-soft">
              {/* Show different messages based on whether we've attempted to load competitors */}
              {!hasMoreCompetitors && initialLoadCompleted.current ? (
                // No competitors found after search
                <div className="py-4">
                  <div className="text-4xl mb-3">🏪</div>
                  <h4 className="text-base font-medium text-primary mb-2">
                    {t(
                      "onboarding.steps.competitors.no_competitors_found",
                      "No Competitors Found"
                    )}
                  </h4>
                  <p className="text-sm text-primary-soft mb-4">
                    {t(
                      "onboarding.steps.competitors.no_competitors_message",
                      `No competitors found in ${selectedBusiness.state || "your area"}. You can add competitors manually using the search above.`
                    )}
                  </p>
                  <div className="text-xs text-primary-soft">
                    <p>
                      {t(
                        "onboarding.steps.competitors.manual_add_tip",
                        "Try searching for competitor names or use the manual entry option"
                      )}
                    </p>
                  </div>
                </div>
              ) : (
                // Initial state - haven't searched yet
                <div>
                  <Button
                    onClick={handleAutoPopulate}
                    variant="secondary"
                    style={{ height: "45px" }}
                    disabled={isLoadingMore || !getBusinessCoordinates()}
                    className="w-full"
                  >
                    {isLoadingMore ? (
                      <span className="inline-block animate-spin mr-2">⏳</span>
                    ) : (
                      <span className="mr-2">🔍</span>
                    )}
                    {t(
                      "onboarding.steps.competitors.auto_populate",
                      "Find Nearby Competitors"
                    )}
                  </Button>

                  <div className="mt-2">
                    <p className="text-xs text-primary-soft text-center">
                      {t(
                        "onboarding.steps.competitors.auto_populate_tip",
                        "Automatically find dispensaries near your location"
                      )}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between mt-4">
        <Button onClick={handleBack} variant="secondary" className="mr-4">
          {t("onboarding.navigation.back")}
        </Button>
        <div className="flex">
          {competitors.length === 0 && !isQuickStart && (
            <Button onClick={handleSkip} variant="secondary" className="mr-4">
              {t("skip_for_now")}
            </Button>
          )}
          <Button
            onClick={handleContinue}
            variant="primary"
            disabled={competitors.length === 0}
          >
            {isQuickStart ? "Lets go!" : t("onboarding.navigation.continue")}
          </Button>
        </div>
      </div>
    </div>
  );
};

// Add CSS for share mode
const styles = `
.share-mode {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 600px;
  position: relative;
  border: 1px solid #e5e7eb;
}

.share-mode .share-attribution {
  display: block !important;
}

.share-mode::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3EDC81, #2bb7da);
  border-radius: 4px 4px 0 0;
}

/* Add styling for attribution */
.share-mode .share-attribution {
  text-align: center;
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.share-mode .share-attribution img {
  height: 20px;
  margin-right: 4px;
}
`;

// Inject the styles
if (typeof document !== "undefined") {
  const styleEl = document.createElement("style");
  styleEl.textContent = styles;
  document.head.appendChild(styleEl);
}

export default CompetitorsStep;
