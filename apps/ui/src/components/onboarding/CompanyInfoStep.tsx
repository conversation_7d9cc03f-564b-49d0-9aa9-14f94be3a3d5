import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Button from "../../ui/Button";
import { Location } from "../../types";
import BusinessLookupDummy from "../../views/location/BusinessLookupDummy";
import ManualEntryDialog from "../dialogs/ManualEntryDialog";

interface CompanyInfoStepProps {
  selectedBusiness: Partial<Location> | null;
  onBusinessSelect: (business: Partial<Location>) => void;
  onStepComplete: () => void;
  isCompleted: boolean;
  isGoogleMapsLoaded: boolean;
  showManualEntry: boolean;
  setShowManualEntry: (show: boolean) => void;
}

const CompanyInfoStep: React.FC<CompanyInfoStepProps> = ({
  selectedBusiness,
  onBusinessSelect,
  onStepComplete,
  isCompleted,
  isGoogleMapsLoaded,
  showManualEntry,
  setShowManualEntry,
}) => {
  const { t } = useTranslation();
  const [isReturningToStep, setIsReturningToStep] = useState(false);
  const [manualEntrySearchQuery, setManualEntrySearchQuery] = useState("");

  // Check if we're in quick start mode
  const location = window.location;
  const params = new URLSearchParams(location.search);
  const isQuickStart = params.get("path") === "quick";

  // Detect if we're returning to this step after navigating away
  useEffect(() => {
    if (selectedBusiness?.name && isCompleted) {
      setIsReturningToStep(true);
    }
  }, [selectedBusiness, isCompleted]);

  const handleSaveBusinessInfo = (businessData: Partial<Location>) => {
    if (!businessData?.name) return;

    try {
      localStorage.setItem(
        "onboarding_business_info",
        JSON.stringify({
          name: businessData.name,
          description: businessData.description || "",
          address: businessData.address,
          city: businessData.city,
          state: businessData.state,
          zip: businessData.zip,
          country: businessData.country,
          phone: businessData.phone,
          website: businessData.website,
          latitude: businessData.latitude,
          longitude: businessData.longitude,
          contact_email: businessData.contact_email,
        })
      );
    } catch (error) {
      console.error("Failed to save business info to localStorage:", error);
    }

    // Call onBusinessSelect to ensure parent components are updated
    onBusinessSelect(businessData);

    // Complete this step
    onStepComplete();
  };

  const handleManualEntryClick = (searchQuery: string) => {
    setManualEntrySearchQuery(searchQuery);
    setShowManualEntry(true);
  };

  return (
    <div className="space-y-3 sm:space-y-4">
      <div>
        <h2 className="text-xl font-semibold text-primary my-1">
          {t("onboarding.steps.company_info.title")}
        </h2>
        <p className="text-base text-primary-soft ms-1">
          {isQuickStart
            ? "Just tell us your location and Smokey's AI will handle the rest"
            : "Smokey's here to help you grow faster—without the marketing overwhelm."}
        </p>
        <p className="text-xs text-primary-soft ms-1">
          {isQuickStart
            ? "Our AI will predict what sells in your area, even without your POS data"
            : "Free forever. Built specifically for dispensaries with no time to waste."}
        </p>
      </div>

      {/* Unified Business Lookup Form */}
      <div>
        {isGoogleMapsLoaded ? (
          <BusinessLookupDummy
            onBusinessSelect={handleSaveBusinessInfo}
            // onManualEntryClick={handleManualEntryClick} // Removed to use internal manual entry logic with geocoding
            selectedBusiness={selectedBusiness}
            isEdit={isCompleted}
            onContinue={() => {}} // No longer needed as handleSaveBusinessInfo handles completion
            forceSearch={isReturningToStep} // Force search view when returning to this step
          />
        ) : (
          <div className="text-center py-4">
            <p className="mt-2 text-primary-soft">{t("loading_places")}</p>
            <Button
              onClick={() => window.location.reload()}
              className="mt-2"
              variant="secondary"
            >
              {t("refresh")}
            </Button>
            <p className="mt-2 text-primary-soft">
              {t("onboarding.steps.company_info.google_api_down")}
            </p>
          </div>
        )}
      </div>

      {/* Manual Entry Dialog */}
      <ManualEntryDialog
        open={showManualEntry}
        onClose={() => setShowManualEntry(false)}
        onSave={handleSaveBusinessInfo}
        searchQuery={manualEntrySearchQuery}
      />
    </div>
  );
};

export default CompanyInfoStep;
