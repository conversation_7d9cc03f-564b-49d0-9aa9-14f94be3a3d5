import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import { Location } from "../../types";
import Button from "../../ui/Button";
import {
  phoneValidator,
  urlValidator,
  postalCodeValidator,
} from "../../utils/validations";
import { miscService } from "../../api/misc";
import { toast } from "react-hot-toast";

interface ManualEntryDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (business: Partial<Location>) => void;
  initialData?: Partial<Location>;
  searchQuery?: string;
  isCompetitor?: boolean;
}

export default function ManualEntryDialog({
  open,
  onClose,
  onSave,
  initialData,
  searchQuery,
  isCompetitor = false,
}: ManualEntryDialogProps) {
  const { t } = useTranslation();
  const [phoneError, setPhoneError] = useState("");
  const [websiteError, setWebsiteError] = useState("");
  const [zipError, setZipError] = useState("");
  const [addressError, setAddressError] = useState("");
  const [isGeocoding, setIsGeocoding] = useState(false);

  // Helper function to validate coordinates
  const validateCoordinates = (lat: number, lng: number): boolean => {
    return (
      !isNaN(lat) &&
      !isNaN(lng) &&
      lat >= -90 &&
      lat <= 90 &&
      lng >= -180 &&
      lng <= 180 &&
      lat !== 0 &&
      lng !== 0
    );
  };

  // Helper function to determine timezone from coordinates
  const getTimezoneFromCoordinates = (lat: number, lng: number): string | undefined => {
    if (!validateCoordinates(lat, lng)) {
      return undefined;
    }

    // Define timezone boundaries for major US regions
    const timezoneRegions = [
      {
        timezone: "America/Los_Angeles",
        bounds: { minLat: 32.5, maxLat: 49.0, minLng: -124.8, maxLng: -114.0 }
      },
      {
        timezone: "America/Denver",
        bounds: { minLat: 31.3, maxLat: 49.0, minLng: -114.0, maxLng: -104.0 }
      },
      {
        timezone: "America/Chicago",
        bounds: { minLat: 25.8, maxLat: 49.4, minLng: -104.0, maxLng: -82.0 }
      },
      {
        timezone: "America/New_York",
        bounds: { minLat: 24.5, maxLat: 47.5, minLng: -82.0, maxLng: -66.9 }
      },
      {
        timezone: "America/Anchorage",
        bounds: { minLat: 54.0, maxLat: 71.5, minLng: -180.0, maxLng: -129.0 }
      },
      {
        timezone: "Pacific/Honolulu",
        bounds: { minLat: 18.9, maxLat: 28.5, minLng: -178.0, maxLng: -154.0 }
      }
    ];

    // Find the timezone region that contains these coordinates
    for (const region of timezoneRegions) {
      const { bounds } = region;
      if (lat >= bounds.minLat && lat <= bounds.maxLat &&
          lng >= bounds.minLng && lng <= bounds.maxLng) {
        console.log(`📍 Determined timezone ${region.timezone} from coordinates (${lat}, ${lng})`);
        return region.timezone;
      }
    }

    console.warn(`⚠️ Could not determine timezone for coordinates (${lat}, ${lng}), leaving blank`);
    return undefined;
  };
  const [formData, setFormData] = useState<Partial<Location>>({
    name: "",
    description: "",
    locale: "en",
    timezone: undefined, // Will be determined from business address location
    latitude: undefined, // Don't set default coordinates - let geocoding handle this
    longitude: undefined, // Don't set default coordinates - let geocoding handle this
    address: "",
    city: "",
    state: "",
    zip: "",
    website: "",
    facebook: "",
    instagram: "",
    twitter: "",
    youtube: "",
    searchMethod: "manual",
  });

  // Phone number validation using our enhanced validator
  const validatePhoneNumber = (phone: string) => {
    if (!phone) return true; // Phone is optional in this form

    if (!phoneValidator(phone)) {
      setPhoneError(t("invalid_phone", "Please enter a valid phone number"));
      return false;
    } else {
      setPhoneError("");
      return true;
    }
  };

  // Website validation using our enhanced validator
  const validateWebsite = (website: string) => {
    if (!website) return true; // Website is optional in this form

    if (!urlValidator(website)) {
      setWebsiteError(t("invalid_website", "Please enter a valid website URL"));
      return false;
    } else {
      setWebsiteError("");
      return true;
    }
  };

  // ZIP/Postal code validation using our enhanced validator
  const validateZip = (zip: string, country = "US") => {
    if (!zip) return true; // ZIP is optional in this form

    if (!postalCodeValidator(zip, country)) {
      setZipError(t("invalid_zip", "Please enter a valid postal code"));
      return false;
    } else {
      setZipError("");
      return true;
    }
  };

  // Address validation
  const validateAddress = (address: string) => {
    if (isCompetitor && !address) {
      setAddressError(t("required_field", "This field is required"));
      return false;
    }
    setAddressError("");
    return true;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPhone = e.target.value;
    setFormData((prev) => ({
      ...prev,
      phone: newPhone,
    }));
    // Clear error when typing
    if (phoneError) {
      setPhoneError("");
    }
  };

  const handleWebsiteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newWebsite = e.target.value;
    setFormData((prev) => ({
      ...prev,
      website: newWebsite,
    }));
    // Clear error when typing
    if (websiteError) {
      setWebsiteError("");
    }
  };

  const handleZipChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newZip = e.target.value;
    setFormData((prev) => ({
      ...prev,
      zip: newZip,
    }));
    // Clear error when typing
    if (zipError) {
      setZipError("");
    }
  };

  useEffect(() => {
    if (initialData) {
      setFormData((prev) => ({ ...prev, ...initialData }));
    } else if (searchQuery) {
      setFormData((prev) => ({ ...prev, name: searchQuery }));
    }
  }, [initialData, searchQuery, open]);

  useEffect(() => {
    if (open) {
      getUserLocation();
    }
  }, [open]);

  const getUserLocation = () => {
    // DISABLED: Do not use machine's current location for business addresses
    // This was causing businesses to get coordinates from the machine's location
    // instead of the business address location
    console.warn("⚠️ Current location detection disabled to prevent coordinate mismatch");
    console.warn("Business coordinates should be determined by the business address, not machine location");
  };

  if (!open) return null;

  const handleSave = async () => {
    // Validate all fields before saving
    const phoneValid = !formData.phone || validatePhoneNumber(formData.phone);
    const websiteValid = isCompetitor
      ? validateWebsite(formData.website || "")
      : !formData.website || validateWebsite(formData.website);
    const zipValid =
      !formData.zip || validateZip(formData.zip, formData.country || "US");
    const addressValid = isCompetitor
      ? validateAddress(formData.address || "")
      : true;

    if (!phoneValid || !websiteValid || !zipValid || !addressValid) {
      return;
    }

    // For competitors, make sure we have a website
    if (isCompetitor && !formData.website) {
      setWebsiteError(t("required_field", "This field is required"));
      return;
    }

    const finalFormData = { ...formData };

    // Try to geocode the address if we have address information
    if (formData.address && formData.city && formData.state) {
      setIsGeocoding(true);

      try {
        const fullAddress = `${formData.address}, ${formData.city}, ${formData.state} ${formData.zip || ''}`.trim();
        console.log(`🏢 Manual Entry Dialog - Starting geocoding process`);
        console.log(`📍 Form data:`, {
          address: formData.address,
          city: formData.city,
          state: formData.state,
          zip: formData.zip
        });
        console.log(`🔍 Manual Entry Dialog - Geocoding address: "${fullAddress}"`);

        const geocodeResponse = await miscService.searchPlaces(fullAddress);
        console.log(`📡 Manual Entry Dialog - Geocode response:`, geocodeResponse);

        if (geocodeResponse?.suggestions && geocodeResponse.suggestions.length > 0) {
          const placeId = geocodeResponse.suggestions[0].placePrediction.placeId;
          console.log(`🆔 Manual Entry Dialog - Using place ID: ${placeId}`);

          const detailResponse = await miscService.geocodePlace(placeId);
          console.log(`📍 Manual Entry Dialog - Detail response:`, detailResponse);

          if (detailResponse?.results && detailResponse.results.length > 0) {
            const result = detailResponse.results[0];
            const lat = result.geometry?.location?.lat;
            const lng = result.geometry?.location?.lng;

            console.log(`🎯 Manual Entry Dialog - Extracted coordinates: lat=${lat}, lng=${lng}`);

            if (lat && lng && validateCoordinates(lat, lng)) {
              finalFormData.latitude = lat;
              finalFormData.longitude = lng;
              finalFormData.timezone = getTimezoneFromCoordinates(lat, lng);
              console.log(
                `✅ Manual Entry Dialog - Successfully geocoded address: lat=${lat}, lng=${lng}, timezone=${finalFormData.timezone}`
              );

              toast.success("Address geocoded successfully!");
            } else {
              console.warn(`❌ Manual Entry Dialog - Invalid coordinates from geocoding: lat=${lat}, lng=${lng}`);
              toast.error("Address found but coordinates are invalid");
            }
          } else {
            console.warn("❌ Manual Entry Dialog - No detail results found");
            toast.error("Could not get detailed location information");
          }
        } else {
          console.warn("No geocoding results found");
          toast.error("Could not find exact coordinates for this address");
        }
      } catch (geocodeError) {
        console.error("Failed to geocode address:", geocodeError);
        toast.error("Failed to geocode address");
      } finally {
        setIsGeocoding(false);
      }
    }

    onSave({
      ...finalFormData,
      searchMethod: "manual",
    });
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-surface rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            {isCompetitor
              ? t(
                  "onboarding.manual_entry.competitor_title",
                  "Enter Competitor Information"
                )
              : t(
                  "onboarding.manual_entry.title",
                  "Enter Business Information"
                )}
          </h3>
          <Button onClick={onClose} variant="secondary">
            ×
          </Button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("business_name")}
              <span className="text-red-500 ml-1">*</span>
            </label>
            <input
              type="text"
              value={formData.name || ""}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  name: e.target.value,
                }))
              }
              required
              className="w-full p-2 border rounded"
              placeholder={t(
                "onboarding.steps.company_info.business_name_placeholder",
                "Enter business name"
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t("address")}
              {isCompetitor && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="text"
              value={formData.address || ""}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  address: e.target.value,
                }))
              }
              onBlur={() =>
                isCompetitor && validateAddress(formData.address || "")
              }
              className={`w-full p-2 border rounded ${
                addressError ? "border-red-500" : ""
              }`}
              placeholder={t("address_placeholder", "Street address")}
              required={isCompetitor}
            />
            {addressError && (
              <p className="text-xs text-red-500 mt-1">{addressError}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                {t("city")}
              </label>
              <input
                type="text"
                value={formData.city || ""}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    city: e.target.value,
                  }))
                }
                className="w-full p-2 border rounded"
                placeholder={t("city_placeholder", "City")}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                {t("state")}
              </label>
              <input
                type="text"
                value={formData.state || ""}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    state: e.target.value,
                  }))
                }
                className="w-full p-2 border rounded"
                placeholder={t("state_placeholder", "State/Province")}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                {t("zip")}
              </label>
              <input
                type="text"
                value={formData.zip || ""}
                onChange={handleZipChange}
                onBlur={() =>
                  formData.zip &&
                  validateZip(formData.zip, formData.country || "US")
                }
                className={`w-full p-2 border ${
                  zipError ? "border-red-500" : "border"
                } rounded`}
                placeholder={t("zip_placeholder", "Postal/ZIP code")}
              />
              {zipError && (
                <p className="text-xs text-red-500 mt-1">{zipError}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">
                {t("phone")}
              </label>
              <input
                type="tel"
                value={formData.phone || ""}
                onChange={handlePhoneChange}
                onBlur={() =>
                  formData.phone && validatePhoneNumber(formData.phone)
                }
                className={`w-full p-2 border ${
                  phoneError ? "border-red-500" : "border"
                } rounded`}
                placeholder={t("phone_placeholder", "Business phone")}
              />
              {phoneError && (
                <p className="text-xs text-red-500 mt-1">{phoneError}</p>
              )}
              <p className="text-xs text-primary-soft mt-1">
                {t("phone_format", "Format: +****************")}
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t("website")}
              {isCompetitor && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="text"
              value={formData.website || ""}
              onChange={handleWebsiteChange}
              onBlur={() =>
                (isCompetitor || formData.website) &&
                validateWebsite(formData.website || "")
              }
              className={`w-full p-2 border ${
                websiteError ? "border-red-500" : "border"
              } rounded`}
              placeholder={t("website_placeholder", "https://example.com")}
              required={isCompetitor}
            />
            {websiteError && (
              <p className="text-xs text-red-500 mt-1">{websiteError}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              {t("description")}
            </label>
            <textarea
              value={formData.description || ""}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              className="w-full p-2 border rounded"
              rows={3}
              placeholder={t(
                "description_placeholder",
                "Brief description of your business"
              )}
            />
          </div>

          <div className="flex justify-end space-x-2 mt-6">
            <Button onClick={onClose} variant="secondary">
              {t("cancel")}
            </Button>
            <Button onClick={handleSave} variant="primary" disabled={isGeocoding}>
              {isGeocoding ? t("geocoding", "Geocoding...") : t("save")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
