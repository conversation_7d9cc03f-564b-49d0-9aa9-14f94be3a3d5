/**
 * Test script to verify the coordinate validation logic
 * This tests the state bounds checking that prevents Illinois coordinates 
 * from being used for California businesses
 */

// Test the coordinate validation logic
function testCoordinateValidation() {
  console.log("🧪 Testing Coordinate Validation Logic");
  console.log("=====================================");

  // Define the same state bounds as in the fix
  const stateBounds = {
    'CA': { minLat: 32.5, maxLat: 42.0, minLng: -124.5, maxLng: -114.1 }, // California
    'California': { minLat: 32.5, maxLat: 42.0, minLng: -124.5, maxLng: -114.1 },
    'IL': { minLat: 36.9, maxLat: 42.5, minLng: -91.5, maxLng: -87.0 }, // Illinois  
    'Illinois': { minLat: 36.9, maxLat: 42.5, minLng: -91.5, maxLng: -87.0 },
  };

  const testCases = [
    {
      name: "California Cannabis <PERSON>rose with Illinois coordinates",
      coordinates: { lat: 42.047961252599116, lng: -87.68866342118447 },
      expectedState: "CA",
      shouldPass: false,
      description: "Illinois coordinates should be rejected for California business"
    },
    {
      name: "California Cannabis Melrose with correct California coordinates",
      coordinates: { lat: 34.09, lng: -118.32 }, // Los Angeles area
      expectedState: "CA", 
      shouldPass: true,
      description: "California coordinates should be accepted for California business"
    },
    {
      name: "Illinois business with Illinois coordinates",
      coordinates: { lat: 42.047961252599116, lng: -87.68866342118447 },
      expectedState: "IL",
      shouldPass: true,
      description: "Illinois coordinates should be accepted for Illinois business"
    },
    {
      name: "Illinois business with California coordinates",
      coordinates: { lat: 34.09, lng: -118.32 },
      expectedState: "IL",
      shouldPass: false,
      description: "California coordinates should be rejected for Illinois business"
    }
  ];

  let passedTests = 0;
  const totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    const bounds = stateBounds[testCase.expectedState];
    const lat = testCase.coordinates.lat;
    const lng = testCase.coordinates.lng;
    
    const withinBounds = lat >= bounds.minLat && lat <= bounds.maxLat && 
                        lng >= bounds.minLng && lng <= bounds.maxLng;
    
    const passed = withinBounds === testCase.shouldPass;
    
    console.log(`\nTest ${index + 1}: ${testCase.name}`);
    console.log(`  Coordinates: lat=${lat}, lng=${lng}`);
    console.log(`  Expected State: ${testCase.expectedState}`);
    console.log(`  Within Bounds: ${withinBounds}`);
    console.log(`  Should Pass: ${testCase.shouldPass}`);
    console.log(`  Result: ${passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Description: ${testCase.description}`);
    
    if (passed) passedTests++;
  });

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log("🎉 All tests passed! The coordinate validation logic is working correctly.");
  } else {
    console.log("⚠️ Some tests failed. The coordinate validation logic needs adjustment.");
  }

  return passedTests === totalTests;
}

// Test the address parsing logic
function testAddressParsing() {
  console.log("\n🧪 Testing Address Parsing Logic");
  console.log("================================");

  const testCases = [
    {
      input: "California Cannabis Melrose at 654 N Manhattan Pl, Los Angeles, CA 90004",
      expected: {
        address: "654 N Manhattan Pl",
        city: "Los Angeles", 
        state: "CA",
        zip: "90004"
      }
    },
    {
      input: "Business Name, 123 Main St, Chicago, IL 60601",
      expected: {
        address: "123 Main St",
        city: "Chicago",
        state: "IL", 
        zip: "60601"
      }
    }
  ];

  testCases.forEach((testCase, index) => {
    const addressMatch = testCase.input.match(/(?:at\s+)?(.+?),?\s*([^,]+),\s*([A-Z]{2})\s*(\d{5})?/i);
    
    console.log(`\nTest ${index + 1}: ${testCase.input}`);
    
    if (addressMatch) {
      const [, possibleAddress, city, state, zip] = addressMatch;
      
      if (/\d/.test(possibleAddress)) {
        const parsed = {
          address: possibleAddress.replace(/^.*?\s+at\s+/i, '').trim(),
          city: city.trim(),
          state: state.trim().toUpperCase(),
          zip: zip?.trim()
        };
        
        console.log(`  Parsed: ${JSON.stringify(parsed)}`);
        console.log(`  Expected: ${JSON.stringify(testCase.expected)}`);
        
        const matches = JSON.stringify(parsed) === JSON.stringify(testCase.expected);
        console.log(`  Result: ${matches ? '✅ PASS' : '❌ FAIL'}`);
      } else {
        console.log(`  ❌ FAIL: Address doesn't contain numbers`);
      }
    } else {
      console.log(`  ❌ FAIL: No address match found`);
    }
  });
}

// Run the tests
if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment
  module.exports = { testCoordinateValidation, testAddressParsing };
} else {
  // Browser environment
  testCoordinateValidation();
  testAddressParsing();
}
